// import 'package:flutter/cupertino.dart';
// import 'package:flutter_bloc/flutter_bloc.dart';
// import 'package:translator/translator.dart';
// import 'settings_cubit.dart';

// class TranslationService {
//   static final GoogleTranslator _translator = GoogleTranslator();

//   static Future<String> translateText(String text, BuildContext context) async {
//     if (text.isEmpty || !context.read<SettingsCubit>().state.internet) {
//       return text;
//     }

//     final String currentLang = context.read<SettingsCubit>().state.locale.languageCode;

//     try {
//       if (currentLang == 'ar') {
//         Translation translation = await _translator.translate(text, to: 'ar');
//         return translation.text;
//       } else if (currentLang == 'en') {
//         Translation translation = await _translator.translate(text, to: 'en');
//         return translation.text;
//       }
//       return text;
//     } catch (e) {
//       return text;
//     }
//   }
// }


// extension StringExtension on String {
//   Future<String> translateText(BuildContext context) async =>
//       await TranslationService.translateText(this, context);
// }
