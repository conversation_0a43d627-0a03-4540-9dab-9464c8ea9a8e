import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:icons_plus/icons_plus.dart';

class AppsToggle extends StatelessWidget {
  AppsToggle({super.key});

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      itemCount: socialApps.length,
      itemBuilder: (context, index) => Padding(
        padding: EdgeInsets.symmetric(horizontal: 15.w, vertical: 3.h),

        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          decoration: BoxDecoration(
            // color: Colors.white,
            borderRadius: BorderRadius.circular(10),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Transform.scale(
                scale: 0.7,
                child: Switch(
                  value: socialApps[index]['icon'],
                  onChanged: (val) {
                  // final name =  socialApps[index]['name'] ;
                  },

                  activeColor: Colors.blueAccent,
                  inactiveThumbColor: Colors.grey,
                ),
              ),
              socialApps[index]['icon'],
            ],
          ),
        ),
      ),
    );
  }

  final List<Map<String, dynamic>> socialApps = [
    {
      "value": false,
      'name': 'WhatsApp',
      'icon': Icon(Bootstrap.whatsapp, color: Color(0xFF25D366)),
    },
    {
      "value": false,
      'name': 'Messenger',
      'icon': Icon(Bootstrap.messenger, color: Color(0xFF0078FF)),
    },
    {
      "value": false,
      'name': 'Telegram',
      'icon': Icon(Bootstrap.telegram, color: Color(0xFF0088CC)),
    },
    {
      "value": false,
      'name': 'Instagram',
      'icon': Icon(Bootstrap.instagram, color: Color(0xFFE4405F)),
    },
    {
      "value": false,
      'name': 'Facebook',
      'icon': Icon(Bootstrap.facebook, color: Color(0xFF1877F2)),
    },
    {
      "value": false,
      'name': 'iMessage',
      'icon': Icon(Bootstrap.chat_left_text_fill, color: Color(0xFF32CD32)),
    },
    {
      "value": false,
      'name': 'Botim',
      'icon': Icon(Bootstrap.chat_dots, color: Color(0xFF00BAF2)),
    },
    {
      "value": false,
      'name': 'IMO',
     'icon': Icon(Bootstrap.chat, color: Color(0xFF00AEEF))},
    {
      "value": false,
      'name': 'Snapchat',
      'icon': Icon(Bootstrap.snapchat, color: Color(0xFFFFFC00)),
    },
    {
      "value": false,
      'name': 'Viber',
      'icon': Icon(Bootstrap.phone_vibrate, color: Color(0xFF7360F2)),
    },
    {
      "value": false,
      'name': 'Google Chat',
      'icon': Icon(Bootstrap.google, color: Color(0xFF25AF5E)),
    },
    {
      "value": false,
      'name': 'LINE',
      'icon': Icon(Bootstrap.chat_left_text, color: Color(0xFF00C300)),
    },
  ];
}
