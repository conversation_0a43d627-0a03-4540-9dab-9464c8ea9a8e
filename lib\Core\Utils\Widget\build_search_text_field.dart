import 'package:al_tarjuman/Core/Utils/Widget/build_text_field.dart';
import 'package:flutter/material.dart';

class BuildSearchTextField extends StatelessWidget {
  const BuildSearchTextField({super.key});

  @override
  Widget build(BuildContext context) {
    return SearchAnchor(
      builder: (builder, controller) {
        return DefaultTextFormField(
          text: "search",
          type: TextInputType.text,
          controller: controller,
          isTextIn: false,
          isTextOut: false,
          isLogin: true,
        );
      },
      suggestionsBuilder: (context, controller) {
        return [];
      },
    );
  }
}
