import 'package:al_tarjuman/Config/Assets/image_svg.dart';
import 'package:al_tarjuman/Core/Resources/text_style.dart';
import 'package:al_tarjuman/Core/Utils/Extensions/localizations_extension.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../Core/Utils/Widget/build_image.dart';

class SplashPage extends StatelessWidget {
  const SplashPage({super.key});

  @override
  Widget build(BuildContext context) {
    
    return SafeArea(child: Column(
      mainAxisAlignment: MainAxisAlignment.center,
      spacing: 32.h,
      children: [
        BuildImageAssets(
          svg: AppImagesSvg.splash,
          width: .5.sw,
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          spacing: 5.w,
          children: [
            Text(
              context.local.name_app,
              style: AppTextStyles.h4Bold,
            ),
            BuildImageAssets(
          svg: AppImagesSvg.lo,
          width: .5.sw,
        ),
          ],
        ),
      ],
    )
    );
  }
}