import 'package:al_tarjuman/Features/Splash/splash_page.dart';
import 'package:flutter/material.dart';
class AppRoutes {
  static const String splash = '/';
  static const String home = '/home';
  static const String translate = '/translate';
  
}

class AppRouteBuilders {
  static Route<dynamic> generateRoute(RouteSettings settings) {
    switch (settings.name) {
      case AppRoutes.splash:
        return _defaultPageRoute(const SplashPage());
      case AppRoutes.home:
        // return _defaultPageRoute(const HomeScreen());
      case AppRoutes.translate:
        // return _defaultPageRoute(const TranslateScreen());
      default:
        return _errorRoute('No route defined for "${settings.name}"');
    }
  }

  static MaterialPageRoute<dynamic> _defaultPageRoute(Widget screen) {
    return MaterialPageRoute(builder: (_) => screen);
  }


  static Route<dynamic> _errorRoute(String message) {
    return MaterialPageRoute(
      builder: (_) => Scaffold(
        body:
            Center(child: Text(message, style: const TextStyle(fontSize: 18))),
      ),
    );
  }
}
