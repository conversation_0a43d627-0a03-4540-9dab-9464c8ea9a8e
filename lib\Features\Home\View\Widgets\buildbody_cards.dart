import '../../../../Core/Utils/Enum/type_cards.dart';
import '../../../../Core/Utils/Extensions/localizations_extension.dart';
import 'build_customer_card.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class BuildbodyCards extends StatelessWidget {
  const BuildbodyCards({super.key});

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceAround,
      mainAxisSize: MainAxisSize.max,
      children: [
        BuildCustomerCard(
          title: context.local.translate_now,
          typeCards: TypeCards.translate,
        ),
        Column(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          mainAxisSize: MainAxisSize.min,
          spacing: 16.h,
          children: [
            BuildCustomerCard(
              title: context.local.translate_now,
              typeCards: TypeCards.micro,
            ),
            BuildCustomerCard(
              title: context.local.translate_now,
              typeCards: TypeCards.chat,
            ),
          ],
        ),
      ],
    );
  }
}
