import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../Config/Cubit/settings_cubit.dart';
import '../../../Config/app_config.dart';
import '../../../generated/l10n.dart';

extension AppLocalizationsExtension on BuildContext {
  S get local => S.of(this);

  bool get isRtl =>
      watch<SettingsCubit>().state.locale == AppConfig.languageMain; 
            
}
