import 'Widgets/build_bigger_card.dart';
import 'Widgets/build_trasnlate_card.dart';
import 'Widgets/buildbody_cards.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../Core/Utils/Widget/default_app_bar.dart';
import 'package:flutter/material.dart';

class HomePage extends StatelessWidget {
  const HomePage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: DefaultAppBar(arowback: false),
      body: SingleChildScrollView(
        child: Align(
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: 15.w),
            child: Column(
              spacing: 25.h,
              children: [
                BuildBiggerCard(),
                BuildbodyCards(),
                BuildTrasnlateCard(),
              ],
            ),
          ),
        ),
      ),
      resizeToAvoidBottomInset: true,
    );
  }
}
