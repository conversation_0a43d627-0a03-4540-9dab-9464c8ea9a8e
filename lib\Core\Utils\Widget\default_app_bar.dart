import 'package:al_tarjuman/Core/Utils/Extensions/context_extension.dart';
import 'package:al_tarjuman/Core/Utils/Widget/apps_toggle.dart';

import '../../../Config/Assets/image_svg.dart';
import '../../Resources/colors.dart';
import '../../Resources/text_style.dart';
import '../Extensions/localizations_extension.dart';
import 'build_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';


class DefaultAppBar extends StatelessWidget implements PreferredSizeWidget {
  const DefaultAppBar({super.key, required this.arowback});
  final bool arowback;
  @override
  Widget build(BuildContext context) {
    return AppBar(
      title: !arowback
          ? Row(
              children: [
                Text(
                  context.local.name_app,
                  style: AppTextStyles.h5Bold.copyWith(
                    color: AppColor.primaryColors,
                  ),
                ),
                10.horizontalSpace,
                BuildImageAssets(svg: AppImagesSvg.logo),
              ],
            )
          : SizedBox(),
      actions: [
        InkWell(
          onTap: () {},
          child: BuildImageAssets(svg: AppImagesSvg.setting),
        ),
        10.horizontalSpace,

        InkWell(
          onTap: () {
            context.buildCustomBottomSheet(
              widgetBuilder: (context) => AppsToggle(),
            );
          },
          child: BuildImageAssets(svg: AppImagesSvg.dotsThree),
        ),
        20.horizontalSpace,
      ],
    );
  }

  @override
  Size get preferredSize => Size.fromHeight(kBottomNavigationBarHeight);
}
